<template>
  <ant-modal
    :visible="open"
    modal-title="预览"
    :loading="modalLoading"
    :modalWidth="width"
    @cancel="cancel"
    :modalHeight="height"
    :footer="false"
  >
    <div style="height: 100%" slot="content">
      <Word v-if="rowInfo?.fileExt === 'docx' || rowInfo?.fileExt === 'doc'" :url="rowInfo.fileUrl" />
      <Excel v-if="rowInfo?.fileExt === 'xlsx' || rowInfo?.fileExt === 'xls'" :url="rowInfo.fileUrl" />
      <Txt v-if="rowInfo?.fileExt === 'txt'" :url="rowInfo.fileUrl" />
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import Word from '@/components/PreviewWord/index.vue'
  import Excel from '@/components/PreviewExcel/index.vue'
  import Txt from '@/components/PreviewTxt/index.vue'

  export default {
    name: 'FormManage',
    components: { AntModal, Word, Excel, Txt },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        width: 0,
        height: 0,
        rowInfo: null,
      }
    },
    created() {
      this.width = `${window.innerWidth * 0.8}`
      this.height = `${window.innerHeight * 0.95}`
    },
    methods: {
      handlePreview(row) {
        this.rowInfo = row
        this.open = true
      },
      cancel() {
        this.open = false
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  ::v-deep .modal-content {
    overflow-x: auto;
    height: 100%;
  }
</style>
