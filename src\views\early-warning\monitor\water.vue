<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="对象名称">
        <a-input v-model="queryParam.objectName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="预警时间">
        <a-range-picker
          allow-clear
          :value="takeEffect"
          format="YYYY-MM-DD"
          formatValue="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          @change="onRangeChange"
        />
      </a-form-item>
      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" v-if="selectIds.length != 0" @click="eliminateAlarms()">消除报警</a-button>
            <!-- <a-button type="danger" v-if="selectIds.length != 0" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button> -->
          </div>
        </VxeTable>
      </template>
    </VxeTableForm>
    <HistoryWaterMonitor
      v-if="showHistoryMonitor"
      ref="historyMonitorRef"
      @ok="onOperation"
      @close="showHistoryMonitor = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getWarnProjectCategory, getWaterWarnEventList, eventClearWaterAlarm } from './services'
  import TreeGeneral from '@/components/TreeGeneral'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'
  import HistoryWaterMonitor from './modules/HistoryWaterMonitor.vue'

  export default {
    name: 'monitor',
    components: {
      VxeTable,
      VxeTableForm,
      TreeGeneral,
      HistoryWaterMonitor,
    },
    data() {
      return {
        treeTabKey: '1',
        currentKeys: [],
        defaultExpandedKeys: [3],
        showHistoryMonitor: false,
        list: [],
        tableTitle: '水量预警监控',
        isChecked: false,
        selectIds: [],
        names: [],
        loading: false,
        total: 0,

        takeEffect: [],
        queryParam: {
          groupId: null,
          objectCode: '',
          objectName: '',
          pageNum: 1,
          pageSize: 10,
          projectId: undefined,
          warnTimeLower: null,
          warnTimeUpper: null,
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '预警编号',
            field: 'eventNo',
            minWidth: 165,
          },
          {
            title: '预警对象',
            field: 'objectName',
            minWidth: 150,
            showOverflow: 'tooltip',
          },
          {
            title: '预警分组',
            field: 'groupName',
            minWidth: 130,
            showOverflow: 'tooltip',
          },
          {
            title: '对象类别',
            field: 'objectCategoryName',
            minWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '预警消息',
            field: 'message',
            minWidth: 280,
            showOverflow: 'tooltip',
          },
          {
            title: '当前状态',
            field: 'status',
            minWidth: 80,
            slots: {
              default: ({ row }) => {
                let statusPointColor = 'common-status-abnormal'
                let statusText = '正在报警'
                if (row.status == 2) {
                  statusPointColor = 'common-status-completed'
                  statusText = '已消除'
                } else if (row.status == 3) {
                  statusPointColor = 'common-status-incomplete'
                  statusText = '已失效'
                }
                return (
                  <div class='common-status-box'>
                    <i class={['common-status-icon', statusPointColor]}></i>
                    <span>{statusText}</span>
                  </div>
                )
              },
            },
          },
          {
            title: '预警时间',
            field: 'createdTime',
            minWidth: 147,
          },
          {
            title: '操作',
            field: 'operate',
            width: 125,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleHistory(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a disabled={row.status != 1} onClick={() => this.eliminateAlarms('one', row)}>
                      消除报警
                    </a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      // // 获取工程树
      // getWarnProjectCategory().then(res => {
      //   this.projectCategory.dataSource = res?.data
      // })
      this.getList()
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.loading = true
        getWaterWarnEventList(this.queryParam).then(response => {
          if (response.data == null) {
            this.loading = false
            return
          }
          this.list = response?.data?.data
          this.total = response?.data?.total
          this.loading = false
        })
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.eventId)
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.takeEffect = []

        this.queryParam = {
          ...this.queryParam,
          objectName: '',
          warnTimeLower: null,
          warnTimeUpper: null,
          pageNum: 1,
        }
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.warnTimeLower = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : null
        this.queryParam.warnTimeUpper = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : null
      },
      // 树加载完成后
      // onTreeMounted(data) {
      //   this.queryParam.projectId = data[0].key
      //   this.getList()
      // },
      // clickTreeNode(node) {
      //   const key = node.$options.propsData.dataRef.key
      //   this.queryParam.projectId = key
      //   this.queryParam.pageNum = 1
      //   this.getList()
      // },
      handleHistory(record) {
        this.showHistoryMonitor = true
        this.$nextTick(() => this.$refs.historyMonitorRef.historyMonitor(record))
      },
      eliminateAlarms(type, row) {
        let ids
        if (type == 'one') {
          ids = row.eventId
        } else {
          ids = this.selectIds.join(',')
        }
        var that = this
        this.$confirm({
          title: '确认消除警报?',
          content: '请确认是否消除警报',
          onOk() {
            eventClearWaterAlarm({ eventIds: ids }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功`, 3)
                that.onOperation()
                that.selectIds = []
              }
            })
          },
          onCancel() {},
        })
      },
      // 操作完成后
      onOperation() {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;

      .ant-tabs-nav-container {
        height: auto;

        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
