<template>
  <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff; padding: 24px">
    <!-- 第一大区块 -->
    <div
      style="display: flex;justify-content: space-between;align-items: center;padding-bottom: 16px;border-bottom: 1px solid #f2f3f5;"
    >
      <div>
        <a-select v-model="selectedForecast" style="width: 350px" placeholder="请选择预报数据">
          <a-select-option v-for="item in forecastOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
      <div>
        <a-button
          type="primary"
          style="color: #fff; background: #165dff; font-size: 14px; font-weight: 400"
          @click="goToComingWaterForecast"
        >
          方案管理
        </a-button>
      </div>
    </div>

    <!-- 内容 -->
    <div style="position: relative; display: flex; flex-direction: row; height: 660px">
      <!-- 地图 -->
      <div style="flex: 5; height: 100%; position: relative">
        <MapBox @onMapMounted="onMapMounted" />
        <MapStyle v-if="!!mapIns"  v-show="false" :mapIns="mapIns" activeStyle="卫星图" ref="mapStyleRef" />
        <div style="width: 100%; height: 44px">
          <TimePlaySlider v-if="times.length && !!mapIns" :times="times" @onTimeChange="onTimeChange" />
        </div>
      </div>
      <div style="flex: 5; height: 100%; width: 100%; padding-left: 20px; display: flex; flex-direction: column">
        <!-- 表数据 -->
        <div style="flex: 39; width: 100%; height: 100%; width: 100%">
          <ResultTable v-if="dataList" :dataSource="dataList" />
        </div>
        <!-- 折线图 -->
        <div style="flex: 22; position: relative">
          <div style="position: absolute; top: 10px; left: 90px; width: 200px; height: 30px; z-index: 1000">
            <a-select
              v-model="hedaoName"
              allowClear
              style="width: 100%; height: 25px; font-weight: 400; font-size: 12px"
              placeholder="请选择"
              :options="hedaoOptions"
              show-search
            ></a-select>
          </div>
          <LineEchart
           :height="'100%'"
            style="margin-top: 20px;"
            :dataSource="lineChartData1"
            :custom="lineChartCustom1"
          ></LineEchart>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getOptions, getValueByKey } from '@/api/common'
  import moment from 'moment'
  import { getImageUrl } from '@/utils/util'
  import ResultTable from './component/ResultTable.vue'
  import ProcessChart from './component/ProcessChart.vue'

  import MapBox from './component/MapBox/index.vue'
  import MapStyle from './component/MapBox/MapStyle.vue'
  import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
  import { MapboxOverlay } from '@deck.gl/mapbox'
  import initLayer from './component/initLayer'
  import { mapboxPopup } from './component/popup.js'

  import { mapBoundGeo } from '@/utils/mapBounds.js'
  import axios from 'axios'
  import { LineEchart } from '@/components/Echarts'
  import MultiPolygon from '@/views/schedule/simulation-model/component/MapBox/MultiPolygon.vue'
  import points from './points.json'
  import mapboxgl from 'mapbox-gl'
  import 'mapbox-gl/dist/mapbox-gl.css'
  import * as turf from '@turf/turf'
  import hedaoData from './data.json'
  import { scaleData } from './config.js'

  export default {
    name: 'ManualForecast',
    components: { MapBox, MapStyle, MultiPolygon, LineEchart, TimePlaySlider, ResultTable, ProcessChart },
    mapIns: null,
    data() {
      return {
        activeProcess: null,
        mapData: null,
        dataList: [],
        mapOverlayIns: null,
        times: [],
        geojson: null,
        showPopupItem: [],
        selectedForecast: undefined,
        forecastOptions: [
          { label: '渠道仿真-2025051817~2025051267', value: '1' },
          { label: '暴雨调度仿真', value: '2' },
          { label: '小雨调度仿真', value: '3' },
        ],
        dataSource: null,
        isTableExpanded: true,
        chartKey: 0,
        lineChartData1: [],
        lineChartCustom1: {
          shortValue: true, // 缩写坐标值
          xLabel: '', // x轴名称
          yLabel: '水位(m)', //y轴名称
          yUnit: '', //y轴单位
          legend: true, // 图例
          showAreaStyle: true, // 颜色区域
          rYUnit: '', // 右侧y轴单位
          rYLabel: '流量(m³/s)', // 右侧y轴名称
          dataZoom: false,
          color: null,
          grid: {
            left: '1%',
            right: '0%',
            bottom: '5%',
            top: '15%',
            containLabel: true,
          },
          legendOptions: {
            orient: 'horizontal',
          },
          legendTop: '1%',
          legendLeft: '65%',
          xAxisData: [],
        },
        columnsDrainage1: [
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '时间',
            field: 'time',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '水位(m)',
            field: 'wlevel',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '流量(m³/s)',
            field: 'q',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
        ],
        listDrainage1: [
          {
            time: '2025-06-12 08:00:00',
            coords: [120.432755755, 30.452525076],
            wlevel: 4.265,
            q: 4.1234,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.432755755, 30.452525076],
            wlevel: 4.265,
            q: 4.1234,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.432755755, 30.452525076],
            wlevel: 4.265,
            q: 4.1234,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.432755755, 30.452525076],
            wlevel: 4.265,
            q: 4.1234,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.432755755, 30.452525076],
            wlevel: 4.265,
            q: 4.1234,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.432579421, 30.454064999],
            wlevel: 4.265,
            q: 5.4824,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.432038747, 30.455509059],
            wlevel: 4.265,
            q: 7.0043,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.431196589, 30.456877102],
            wlevel: 4.265,
            q: 12.617,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.430385053, 30.458257812],
            wlevel: 4.265,
            q: 12.6087,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.429671994, 30.459679297],
            wlevel: 4.265,
            q: 12.5991,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.429101548, 30.46081647],
            wlevel: 4.265,
            q: 15.827,
          },
          {
            time: '2025-06-12 08:00:00',
            coords: [120.428531101, 30.46195363],
            wlevel: 4.265,
            q: 19.059,
          },
        ],
        hedaoOptions: [],
        hedaoName: '',
        lineName: '',
        lineOptions: [],
      }
    },
    watch: {
      lineName(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.lineChartData = [{ name: newVal, color: '#B5E241', data: this.getAreaDataByName(newVal) }]
        }
      },
      hedaoName(newVal, oldVal) {
        if (newVal !== oldVal) {
          let dataArr = this.extractData(newVal)
          let data3 = []
          let data1 = []
          let data2 = []
          dataArr.forEach(element => {
            if (element.time == '2025-06-12 08:00:00') {
              data3.push(element.distance)
              data1.push(+element.wlevel)
              data2.push(+element.q)
            }
          })

          let res = [
            {
              name: '水位(m)',
              color: '#507EF7',
              yAxisIndex: 0,
              data: data1,
            },
            {
              name: '流量(m³/s)',
              color: '#B5E241',
              yAxisIndex: 1,
              data: data2,
            },
          ]
          this.lineChartCustom1.xAxisData = data3?.map(item => [item])
          this.lineChartData1 = res
        }
      },
    },
    created() {
      // 初始化选择第一个选项
      if (this.forecastOptions.length > 0) {
        this.selectedForecast = this.forecastOptions[0].value
      }
      getOptions('scaleProjectCode').then(res => {
        this.showPopupItem = res.data.map(el => ({ projectCode: el.key, projectName: el.value }))
        this.onTimeChange(this.currentTime)
      })
      this.init()
      this.getList()
    },
    methods: {
      init() {
        this.dataList = scaleData
        this.mapData = scaleData
        this.times = [...new Set(this.mapData[0].resVOS.map(el => el.tm))]
      },
      headerCellClassName({ column }) {
        return 'col-blue'
      },
      rowClassName() {
        return 'row-green'
      },
      getData(mapIns) {
        let shuiDongLi =
          'https://www.sthzxgq.cn:11120/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=sthgq_vector%3AHP005&maxFeatures=50000&outputFormat=application%2Fjson'
        let hoveredPolylineId = null
        axios(shuiDongLi).then(resp => {
          let tempArr = []
          tempArr = scaleData.reduce((acc, item) => {
            acc.push({
              value: item.projectCode,
              label: item.projectName
            })
            return acc
          }, [])
          this.hedaoOptions = tempArr
          this.hedaoName = tempArr[0].value
          // console.log('**** hedaoname', this.hedaoName, tempArr)
          const self = this
          mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
          mapIns.addSource('shuiDongLiSource', {
            type: 'geojson',
            data: resp.data, //区划的面数据
            generateId: true, // 确保所有特征都有唯一的ID
          })

          mapIns.addInteraction('gq-area-shuiDongLi-click-interaction', {
            type: 'click',
            target: { layerId: 'gq-area-shuiDongLi' },
            handler: e => {
              const coordinates = turf.centroid(turf.polygon(e.feature.geometry.coordinates)).geometry.coordinates
              new mapboxgl.Popup().setLngLat(coordinates).setHTML(description).addTo(mapIns)
            },
          })

          mapIns.on('mousemove', 'gq-area-shuiDongLi', e => {
            if (e.features.length > 0) {
              if (hoveredPolylineId !== null) {
                mapIns.setFeatureState({ source: 'shuiDongLiSource', id: hoveredPolylineId }, { hover: false })
              }
              hoveredPolylineId = e.features[0].id
              mapIns.setFeatureState({ source: 'shuiDongLiSource', id: hoveredPolylineId }, { hover: true })
            }
          })
          mapIns.on('mouseleave', 'gq-area-shuiDongLi', () => {
            if (hoveredPolylineId !== null) {
              mapIns.setFeatureState({ source: 'shuiDongLiSource', id: hoveredPolylineId }, { hover: false })
            }
            hoveredPolylineId = null
          })
        })

        mapIns.addSource('points', {
          type: 'geojson',
          data: points, //区划的面数据
          generateId: true, // 确保所有特征都有唯一的ID
        })

        mapIns.addInteraction('gq-area-points-click-interaction', {
          type: 'click',
          target: { layerId: 'gq-area-points' },
          handler: e => {
            // Copy coordinates array.
            const coordinates = e.feature.geometry.coordinates

            console.log(' e.feature.properties.ts ', e.feature.properties.ts, JSON.parse(e.feature.properties.ts))
            this.listDrainage1 = JSON.parse(e.feature.properties.ts).map(item => {
              return {
                time: item[0],
                wlevel: item[1],
                q: item[2],
              }
            })

            new mapboxgl.Popup()
              .setLngLat(coordinates)
              .setHTML(
                `<div><div>水位：${this.listDrainage1[1].wlevel}</div><div>流量：${this.listDrainage1[2].wlevel}</div></div>`,
              )
              .addTo(mapIns)
          },
        })
      },
      onMapMounted(mapIns) {
        this.mapIns = mapIns
        this.$nextTick(() => {
          this.mapIns.resize()
        })
        this.mapOverlayIns = new MapboxOverlay({
          id: 'deck-geojson-layer-overlay',
          layers: [],
        })
        this.mapIns.addControl(this.mapOverlayIns)
        this.dealLayers()
        this.getData(mapIns)
      },
      extractData(hedaoId) {
        function calculateDistance(coord1, coord2) {
          const [lon1, lat1] = coord1
          const [lon2, lat2] = coord2
          const R = 6371e3 // 地球半径，单位为米
          const φ1 = lat1 * (Math.PI / 180)
          const φ2 = lat2 * (Math.PI / 180)
          const Δφ = (lat2 - lat1) * (Math.PI / 180)
          const Δλ = (lon2 - lon1) * (Math.PI / 180)

          const a =
            Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
          const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
          return Math.floor(R * c)
        }
        let newId =
          hedaoId == 4696
            ? 10025
            : hedaoId == 3671
              ? 20030
              : hedaoId == 4697
                ? 10041
                : hedaoId == 3651
                  ? 10023
                  : hedaoId == 3634
                    ? 20025
                    : 10030
        if (!newId) {
          return []
        }
        if (!('GQ' + newId in hedaoData)) return []
        const { coord, data } = hedaoData['GQ' + newId] // GQ10016
        const result = []
        let cumulativeDistance = 0
        const distances = [0]

        for (let i = 1; i < coord.length; i++) {
          cumulativeDistance += calculateDistance(coord[i - 1], coord[i])
          distances.push(cumulativeDistance)
        }

        for (const time in data) {
          const { wlevel, q } = data[time]
          distances.forEach((distance, index) => {
            result.push({
              time,
              distance,
              wlevel: wlevel[index],
              q: q[index],
            })
          })
        }
        return result
      },
      getList() {
        // 使用与automatic-forecast相同的接口获取数据
        // queryAutoForecast({
        //   fcstRange: this.selectedForecast,
        //   startTime: '2025-06-25 14:00',
        //   endTime: '2025-06-28 14:00',
        // }).then(res => {
        //   this.dataSource = res.data
        // })
      },
      goToComingWaterForecast() {
        this.$router.push('/schedule/simulation-model-case')
      },

      onTimeChange(time) {
        this.currentTime = time

        if (this.mapData.length > 0) {
          let arr = []
          this.mapData.forEach(el => {
            arr = arr.concat(el.resVOS.find(ele => ele.tm == time)?.records)
          })
          const factArr = arr.filter(el => !!+el?.longitude && !!+el?.latitude)
          if (factArr.length === 0) return
          this.geojson = {
            type: 'FeatureCollection',
            features: factArr.map(el => {
              return {
                type: 'Feature',
                geometry: {
                  type: 'Point',
                  coordinates: [+el.longitude, +el.latitude],
                },
                properties: {
                  ...el,
                },
              }
            }),
          }

          if (!this.mapOverlayIns?._props?.layers?.length > 0) {
            this.dealLayers()
          }

          this.handleOpenPopup(factArr)

          if (!!this.activeProcess) {
            this.activeProcess = {
              chartData: this.activeProcess.chartData,
              ...factArr.find(el => el.projectId === this.activeProcess.projectId),
            }
          }
        }
        if (!!this.dataSource) {
          this.markLineXAxis1 = this.dataSource['GQ2701'].bottom.length - 1

          const stakes = this.dataSource['GQ2701'].stakes.concat(
            this.dataSource['**********'].stakes
              .slice(0, this.westMainCanalForkIndex)
              .map(el => el + this.dataSource['GQ2701'].stakes[this.dataSource['GQ2701'].stakes.length - 1]),
          )

          this.chartData = {
            chart1: [
              {
                name: '水位',
                color: '#72C5F5',
                data: this.dataSource['GQ2701'].data[this.currentTime + ':00'].wlevel
                  .concat(
                    this.dataSource['**********'].data[this.currentTime + ':00'].wlevel.slice(
                      0,
                      this.dataSource['**********'].bottom.length,
                    ),
                  )
                  .map((el, idx) => [idx, el]),
              },
              {
                name: '渠底',
                color: '#D1CBA8',
                data: this.dataSource['GQ2701'].bottom
                  .concat(this.dataSource['**********'].bottom)
                  .map((el, idx) => [idx, el]),
              },
              {
                name: '流量',
                color: '#F7BA1E',
                yAxisIndex: 1,
                data: this.dataSource['GQ2701'].data[this.currentTime + ':00'].q
                  .concat(
                    this.dataSource['**********'].data[this.currentTime + ':00'].q.slice(
                      0,
                      this.dataSource['**********'].bottom.length,
                    ),
                  )
                  .map((el, idx) => [idx, el]),
              },
            ],
            chart1Stakes: this.dataSource['GQ2701'].stakes.concat(
              this.dataSource['**********'].stakes.map(
                el => el + this.dataSource['GQ2701'].stakes[this.dataSource['GQ2701'].stakes.length - 1],
              ),
            ),
            chart2: [
              {
                name: '水位',
                color: '#72C5F5',
                data: this.dataSource['GQ2701'].data[this.currentTime + ':00'].wlevel
                  .concat(
                    this.dataSource['**********'].data[this.currentTime + ':00'].wlevel.slice(
                      0,
                      this.dataSource['**********'].bottom.length,
                    ),
                  )
                  .map((el, idx) => [idx, el]),
              },
              {
                name: '渠底',
                color: '#D1CBA8',
                data: this.dataSource['GQ2701'].bottom
                  .concat(this.dataSource['**********'].bottom)
                  .map((el, idx) => [idx, el]),
              },
              {
                name: '流量',
                color: '#F7BA1E',
                yAxisIndex: 1,
                data: this.dataSource['GQ2701'].data[this.currentTime + ':00'].q
                  .concat(
                    this.dataSource['**********'].data[this.currentTime + ':00'].q.slice(
                      0,
                      this.dataSource['**********'].bottom.length,
                    ),
                  )
                  .map((el, idx) => [idx, el]),
              },
            ],
            chart2Stakes: this.dataSource['GQ2701'].stakes.concat(
              this.dataSource['**********'].stakes.map(
                el => el + this.dataSource['GQ2701'].stakes[this.dataSource['GQ2701'].stakes.length - 1],
              ),
            ),
            chart3: [
              {
                name: '水位',
                color: '#72C5F5',
                data: this.dataSource['GQ2701'].data[this.currentTime + ':00'].wlevel
                  .concat(
                    this.dataSource['**********'].data[this.currentTime + ':00'].wlevel.slice(
                      0,
                      this.westMainCanalForkIndex,
                    ),
                  )
                  .concat(this.dataSource['**********'].data[this.currentTime + ':00'].wlevel)
                  .map((el, idx) => [idx, el]),
              },
              {
                name: '渠底',
                color: '#D1CBA8',
                data: this.dataSource['GQ2701'].bottom
                  .concat(this.dataSource['**********'].bottom.slice(0, this.westMainCanalForkIndex))
                  .concat(this.dataSource['**********'].bottom)
                  .map((el, idx) => [idx, el]),
              },
              {
                name: '流量',
                color: '#F7BA1E',
                yAxisIndex: 1,
                data: this.dataSource['GQ2701'].data[this.currentTime + ':00'].q
                  .concat(
                    this.dataSource['**********'].data[this.currentTime + ':00'].q.slice(
                      0,
                      this.westMainCanalForkIndex,
                    ),
                  )
                  .concat(this.dataSource['**********'].data[this.currentTime + ':00'].q)
                  .map((el, idx) => [idx, el]),
              },
            ],
            chart3Stakes: stakes.concat(this.dataSource['**********'].stakes.map(el => el + stakes[stakes.length - 1])),
          }
        }
      },
      handleOpenPopup(factArr) {
        if (this.showPopupItem.length > 0 && !!this.mapIns) {
          this.showPopupItem.forEach(el => {
            el?.popupIns?.remove()

            if (factArr.some(ele => ele.projectCode === el.projectCode)) {
              this.dealPopup(factArr.find(ele => ele.projectCode === el.projectCode))
            }
          })
        }
      },
      dealLayers() {
        if (!!this.mapIns && !!this.geojson) {
          initLayer(this.mapIns, this.mapOverlayIns, this.geojson, item => {
            if (this.showPopupItem.every(el => el.projectCode !== item.projectCode)) {
              this.dealPopup(item)
            }
          })
        }
      },

      dealPopup(curr) {
        const popupIns = mapboxPopup(this.mapIns, {
          ...curr,
          lngLat: [+curr.longitude, +curr.latitude],
          onPopupClose: item => {
            const index = this.showPopupItem.findIndex(el => el.projectCode === item.projectCode)
            this.showPopupItem[index].popupIns.remove()
            this.showPopupItem = this.showPopupItem.filter((el, i) => i !== index)
          },
          onProcessClick: item => {
            getChSimResList({ chSimId: this.chSimId, projectId: item.projectId }).then(res => {
              this.activeProcess = { ...item, chartData: res.data }
            })
          },
        })

        popupIns.getElement().style['z-index'] = '11'

        let index = this.showPopupItem.findIndex(el => el.projectCode === curr.projectCode)
        if (index === -1) {
          this.showPopupItem.push({ projectCode: curr.projectCode, popupIns })
        } else {
          this.showPopupItem[index] = { ...this.showPopupItem[index], popupIns }
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .summary {
    display: flex;
    justify-content: space-between;
    .hgroup {
      width: 19%;
      padding: 24px 26px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 12px;

      .content {
        flex: 1;
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .num {
        font-weight: 700;
        font-size: 24px;
        color: #1d2129;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 0;
      }
      .text {
        font-size: 14px;
        color: #4e5969;
        font-weight: 400;
        margin: 0;
      }
      .unit {
        margin-left: -2px;
        font-size: 14px;
      }
      .icon {
        width: 50px;
        height: 50px;
        display: inline-block;
        flex-shrink: 0;
      }
      &:nth-child(1) {
        .icon {
          background: url('@/assets/images/three-days-rain.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(2) {
        .icon {
          background: url('@/assets/images/all-rain.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(3) {
        .icon {
          background: url('@/assets/images/coming-water.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(4) {
        .icon {
          background: url('@/assets/images/flood-peak.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(5) {
        .icon {
          background: url('@/assets/images/flood-peak-time.png') 0 0 no-repeat;
          background-size: 100%;
        }
        .num {
          font-size: 15px;
        }
      }
    }
  }

  .flood-box {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;

    .flood-tabs {
      margin: 0;
      .name {
        font-size: 20px;
        color: #1d2129;
        font-weight: 600;
      }
    }

    .flood-content {
      flex: 1;
    }
  }

  @font-face {
    font-family: 'AlimamaDaoLiTi';
    src: url('@/assets/font/AlimamaDaoLiTi.ttf');
  }
</style>
