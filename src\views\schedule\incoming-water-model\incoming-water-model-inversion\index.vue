<template>
  <div class="common-table-page" style="display: flex; flex-direction: column; background: #ffffff; padding: 24px">
    <!-- 上部分：标题和返回按钮 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
      <h2 style="margin: 0; font-size: 20px; color: #1d2129; font-weight: 600;">来水反演</h2>
      <a-button @click="goBack" style="color: #4E5969; background: #fff; border-color: #E5E6EB; font-size: 14px; font-weight: 400;">
        返回
      </a-button>
    </div>

    <!-- 下部分 -->
    <div style="flex: 1; display: flex; flex-direction: column;">
      <!-- 时间选择器和按钮区域 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="color: #4E5969; font-size: 14px;">时间范围:</span>
          <a-range-picker
            v-model="timeRange"
            show-time
            format="YYYY-MM-DD HH:mm"
            style="width: 350px;"
            placeholder="['开始时间', '结束时间']"
            :disabled-date="disabledDate"
            @change="onTimeRangeChange"
          />
        </div>
        <div>
          <a-button @click="handleReset" style="margin-right: 16px; color: #4E5969; background: #fff; border-color: #E5E6EB; font-size: 14px; font-weight: 400;">
            重置
          </a-button>
          <a-button type="primary" @click="handleQuery" style="color: #fff; background: #165DFF; font-size: 14px; font-weight: 400;">
            查询
          </a-button>
        </div>
      </div>

      <!-- 图表和表格展示区域 -->
      <div class="flood-box">
        <div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 16px;">
          <!-- <p class="flood-tabs">
            <label class="name">来水反演</label>
          </p> -->
          <a @click="toggleTable" style="color: #165DFF; cursor: pointer; font-size: 14px;">
            {{ isTableExpanded ? '表格收起' : '表格展开' }}
          </a>
        </div>
        
        <div class="flood-content" :style="{ display: 'flex', flexDirection: 'row', width: '100%' }">
          <div :style="{ width: isTableExpanded ? '50%' : '100%' }">
            <ResultChart v-if="!!dataSource" :dataSource="dataSource" :scenario="'inversion'" :showForecastBaseline="false" :key="chartKey" />
          </div>
          <div v-if="isTableExpanded" style="width: 50%; margin-left: 16px;">
            <ResultTable :dataSource="dataSource?.reals || []" :scenario="'inversion'" :isShowTableHeader="false" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getInWaterResDetails } from '../services'
  import ResultChart from '../components/Chart.vue'
  import ResultTable from '../components/ResultTable.vue'
  import moment from 'moment'

  export default {
    name: 'IncomingWaterInversion',
    components: { ResultChart, ResultTable },

    data() {
      return {
        timeRange: [
          moment().subtract(10, 'days'),
          moment()
        ],
        dataSource: null,
        isTableExpanded: true,
        chartKey: 0,
      }
    },
    
    created() {
      this.getList();
    },
    
    methods: {
      getList() {
        const startTime = this.timeRange[0] ? this.timeRange[0].format('YYYY-MM-DD HH:mm:ss') : '';
        const endTime = this.timeRange[1] ? this.timeRange[1].format('YYYY-MM-DD HH:mm:ss') : '';

        // 调用来水反演接口
        getInWaterResDetails({
          startTime: startTime,
          endTime: endTime,
        }).then(res => {
          if (res.success && res.data) {
            // 转换数据格式以适配现有的图表组件
            // 来水反演数据作为实际数据展示，不区分预测和实际
            let cumulativeRain = 0;
            let previousStorageValue = null;

            const transformedData = res.data.map((item, index) => {
              // 计算累计降雨量
              cumulativeRain += (item.rain || 0);

              // 计算库容变化量
              let storageChange = 0;
              if (previousStorageValue !== null) {
                storageChange = (item.storageValue || 0) - previousStorageValue;
              }
              previousStorageValue = item.storageValue || 0;

              return {
                inflow: item.inflow || 0,
                outflow: item.outWater || 0, // 注意这里字段名的转换
                rain: item.rain || 0,
                sumRain: +cumulativeRain.toFixed(1), // 累计降雨量
                storageValue: item.storageValue || 0,
                storageChange: +storageChange.toFixed(1), // 库容变化量
                tm: item.tm,
                wlv: item.wlv || 0
              };
            });

            this.dataSource = {
              reals: transformedData, // 来水反演数据作为实际数据
              fcsts: [] // 预测数据为空
            };
          }
        }).catch(error => {
          console.error('获取来水反演数据失败:', error);
          this.$message.error('获取数据失败，请稍后重试');
        });
      },
      
      handleQuery() {
        if (!this.timeRange || !this.timeRange[0] || !this.timeRange[1]) {
          this.$message.warning('请选择时间范围');
          return;
        }

        // 验证时间范围
        if (!this.validateTimeRange()) {
          return;
        }

        this.getList();
      },

      handleReset() {
        this.timeRange = [
          moment().subtract(10, 'days'),
          moment()
        ];
        this.getList();
      },

      // 验证时间范围
      validateTimeRange() {
        const startTime = this.timeRange[0];
        const endTime = this.timeRange[1];
        const now = moment();

        // 检查是否选择了未来时间
        if (endTime.isAfter(now)) {
          this.$message.warning('结束时间不能超过当前时间');
          return false;
        }

        // 检查时间区间是否超过30天
        const daysDiff = endTime.diff(startTime, 'days');
        if (daysDiff > 30) {
          this.$message.warning('时间区间不能超过30天');
          return false;
        }

        return true;
      },

      // 时间范围变化处理
      onTimeRangeChange(dates) {
        if (dates && dates.length === 2) {
          this.validateTimeRange();
        }
      },

      // 禁用未来日期
      disabledDate(current) {
        return current && current > moment().endOf('day');
      },
      
      toggleTable() {
        this.isTableExpanded = !this.isTableExpanded;
        this.$nextTick(() => {
          this.chartKey += 1;
        });
      },
      
      goBack() {
        this.$router.go(-1);
      }
    },
  }
</script>

<style lang="less" scoped>
  .flood-box {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .flood-tabs {
      margin: 0;
      .name {
        font-size: 20px;
        color: #1d2129;
        font-weight: 600;
      }
    }
    
    .flood-content {
      flex: 1;
    }
  }

  @font-face {
    font-family: 'AlimamaDaoLiTi';
    src: url('@/assets/font/AlimamaDaoLiTi.ttf');
  }
</style>
