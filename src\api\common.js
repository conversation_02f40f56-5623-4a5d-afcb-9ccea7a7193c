import request from '@/utils/request'

// 获取字典下拉选项
export function getOptions(dictCode) {
  return request({
    url: '/sys/dict/getOptions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { dictCode },
  })
}

// 获取参数配置值
export function getValueByKey(configKey) {
  return request({
    url: '/sys/config/getValueByKey',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { configKey },
  })
}

// 机构部门人员树查询
export function getDeptUserTree(params) {
  return request({
    url: '/sys/dept/user/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取人员
export function getSysUserPage(data) {
  return request({
    url: '/sys/user/page',
    method: 'post',
    data,
  })
}

// 行政区划-获取树
export function getDistrictTree(params) {
  return request({
    url: '/base/district/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 行政区划工程数-获取树
export function getDistrictProjectTree(params) {
  return request({
    url: '/base/district/project/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 工程信息-获取树
export function getProjectTree(data) {
  return request({
    url: '/base/project/getTree',
    method: 'post',
    data,
  })
}
// 工程信息-获取树(含节点数量)
export function getTreeWithCount(data) {
  return request({
    url: '/base/project/getTreeWithCount',
    method: 'post',
    data,
  })
}

// 工程信息-获取水位阈值树
export function getThresholdProjectTree(params) {
  return request({
    url: '/base/threshold/project/tree',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 工程信息-获取子节点
export function getProjectChildren(params) {
  return request({
    url: '/base/project/getChildren',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程信息-获取分类工程树
export function getProjectCategoryTree(params) {
  return request({
    url: '/base/project/category/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 设备分类-获取树
export function getDeviceCategoryTree(params) {
  return request({
    url: '/base/deviceCategory/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 监测站点-获取分类站点树
export function getSiteCategoryTree(params) {
  return request({
    url: '/war/history/category/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, labels: '1' },
  })
}

// 监测站点-获取行政区划站点树
export function getSiteDistrictTree(params) {
  return request({
    url: '/war/history/DistrictSite/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 监测站点-获取水系站点树//流量
export function getSiteRiverTree(params) {
  return request({
    url: '/war/history/RiverSite/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 水利对象分类-根据编码获取树--站点类别
export function getSideTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'MS' },
  })
}

// 水利对象分类-根据编码获取树-工程类别，分时阈值用
export function getCategoryTreeByCode(params) {
  return request({
    url: '/base/objectCategory/getTreeByCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { ...params, objectCategoryCode: 'HP' },
  })
}

//有权限登录的机构列表
export function loginOrgList() {
  return request({
    url: '/sys/dept/loginOrg/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//切换登录机构
export function switchLoginOrg(params) {
  return request({
    url: '/sys/switchLoginOrg',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//获取当前登录用户信息
export function getUserInfo(params) {
  return request({
    url: '/sys/user/profile',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//查询当前登录机构的部门树
export function getTreeByLoginOrgId(params) {
  return request({
    url: '/sys/dept/treeByLoginOrgId',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//部门树结构
export function getConfigTree(data) {
  return request({
    url: '/sys/dept/tree',
    method: 'post',
    // data: { ...data, type: '1' },
    data,
  })
}
//单位树
export function getDeptSiteTree(params) {
  return request({
    url: '/war/history/deptSite/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//用户列表
export function getComUserList(params) {
  return request({
    url: '/sys/user/user/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 工程附件列表查询
export function getAttachList(data) {
  return request({
    url: '/prjstd/attach/list',
    method: 'post',
    data,
  })
}

// 工程附件新增
export function addAttach(data) {
  return request({
    url: '/prjstd/attach/add',
    method: 'post',
    data,
  })
}

// 工程附件删除
export function deleteAttach(params) {
  return request({
    url: '/prjstd/attach/delete',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
// 新增-----预警左侧树
export function addBaseCategory(data) {
  return request({
    url: '/base/category/add',
    method: 'post',
    data,
  })
}
// 修改-----预警左侧树
export function editBaseCategory(data) {
  return request({
    url: '/base/category/update',
    method: 'post',
    data,
  })
}
//列表-----预警左侧树
export function getBaseCategory(params) {
  return request({
    url: '/base/category/tree',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//删除-----预警左侧树
export function deleteBaseCategory(params) {
  return request({
    url: '/base/category/delete',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

//人员选择弹框
export function baseContactTree(params) {
  return request({
    url: '/base/contact/tree',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

//自定义人员选择弹框
export function baseContactGroupTree(params) {
  return request({
    url: '/base/contact/group/group/tree',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 列表-----设备分类列表
export function getDeviceCategoryAllList(data) {
  return request({
    url: '/base/deviceCategory/page',
    method: 'post',
    data,
  })
}
// 列表-----工程列表
export function getProjectAllList(data) {
  return request({
    url: '/base/project/page',
    method: 'post',
    data,
  })
}

/**
 * 水系列表
 * @returns
 */
export function getRiverSystemList() {
  return request({
    url: '/base/riverSystem/list',
    method: 'post',
  })
}

//列表--圩区列表
export function getPolderList(params) {
  return request({
    url: '/view/projectDevice/getPolderList',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

//监测指标 详情
export function getObjectIndex(data) {
  return request({
    url: '/base/object/index/get',
    method: 'post',
    data,
  })
}
//监测指标 新增
export function addObjectIndex(data) {
  return request({
    url: '/base/object/index/add',
    method: 'post',
    data,
  })
}
//监测指标 删除
export function deleteObjectIndex(params) {
  return request({
    url: '/base/object/index/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水利对象分类-一级分类 
export function objectCategoryFirstLevelList() {
  return request({
    url: '/base/objectCategory/firstLevel/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
