<template>
  <div class="preview-txt">
    <div class="preview-header">
      <div class="file-info">
        <span class="file-name">{{ fileName }}</span>
        <span class="file-size" v-if="fileSize">{{ formatFileSize(fileSize) }}</span>
      </div>
      <div class="preview-actions">
        <button class="action-btn" @click="copyContent" :disabled="!content" title="复制内容">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"
            />
          </svg>
        </button>
      </div>
    </div>

    <div class="preview-content" :class="{ loading: loading, error: error }">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <span>正在加载文件内容...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" />
        </svg>
        <p class="error-message">{{ error }}</p>
        <button class="retry-btn" @click="loadContent">重试</button>
      </div>

      <!-- 内容预览 -->
      <div v-else-if="content" class="content-wrapper">
        <pre class="content-text">{{ content }}</pre>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
        </svg>
        <p>请提供有效的文件地址</p>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="preview-footer" v-if="content">
      <span class="content-stats">行数: {{ lineCount }} | 字符数: {{ charCount }}</span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'PreviewTxt',
    props: {
      // 文件URL地址
      url: {
        type: String,
        required: true,
        default: '',
      },
    },

    data() {
      return {
        loading: false,
        error: null,
        content: '',
        fileSize: 0,
      }
    },

    computed: {
      // 从URL提取文件名
      fileName() {
        if (!this.url) return '文本文件'
        const urlParts = this.url.split('/')
        const fileName = urlParts[urlParts.length - 1]
        return fileName || '文本文件'
      },

      // 行数统计
      lineCount() {
        return this.content ? this.content.split('\n').length : 0
      },

      // 字符数统计
      charCount() {
        return this.content ? this.content.length : 0
      },
    },

    watch: {
      url: {
        handler(newUrl) {
          if (newUrl) {
            this.loadContent()
          } else {
            this.content = ''
            this.error = null
          }
        },
        immediate: true,
      },
    },

    methods: {
      // 加载文件内容
      async loadContent() {
        if (!this.url) {
          this.error = '请提供有效的文件地址'
          return
        }

        this.loading = true
        this.error = null
        this.content = ''

        try {
          const response = await fetch(this.url)

          if (!response.ok) {
            throw new Error(`加载失败: ${response.status} ${response.statusText}`)
          }

          // 获取文件大小
          const contentLength = response.headers.get('content-length')
          if (contentLength) {
            this.fileSize = parseInt(contentLength)
          }

          // 检查文件大小限制（10MB）
          if (this.fileSize > 10 * 1024 * 1024) {
            throw new Error('文件过大，超过 10MB 限制')
          }

          const text = await response.text()
          this.content = text

          // 如果没有从响应头获取到大小，使用文本长度
          if (!this.fileSize) {
            this.fileSize = new Blob([text]).size
          }

          this.$emit('load', {
            content: text,
            size: this.fileSize,
            lines: this.lineCount,
            url: this.url,
          })
        } catch (err) {
          this.error = err.message || '加载文件失败'
          this.$emit('error', {
            error: err,
            url: this.url,
          })
        } finally {
          this.loading = false
        }
      },

      // 复制内容到剪贴板
      async copyContent() {
        if (!this.content) return

        try {
          await navigator.clipboard.writeText(this.content)
          this.$message?.success('内容已复制到剪贴板')
          this.$emit('copy', this.content)
        } catch (err) {
          console.error('复制失败:', err)
          this.$message?.error('复制失败')
        }
      },

      // 格式化文件大小
      formatFileSize(bytes) {
        if (bytes === 0) return '0 B'

        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      },
    },
  }
</script>

<style lang="less" scoped>
  .preview-txt {
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    background: #fff;
    overflow: hidden;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #e8e8e8;

      .file-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .file-name {
          font-weight: 500;
          color: #262626;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          font-size: 12px;
          color: #8c8c8c;
          background: #f0f0f0;
          padding: 2px 6px;
          border-radius: 3px;
        }
      }

      .preview-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          background: #fff;
          color: #595959;
          cursor: pointer;
          transition: all 0.2s;

          &:hover:not(:disabled) {
            border-color: #40a9ff;
            color: #40a9ff;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
    }

    .preview-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      &.loading,
      &.error {
        justify-content: center;
        align-items: center;
      }

      .loading-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        color: #8c8c8c;

        .loading-spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #f0f0f0;
          border-top: 3px solid #1890ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }

      .error-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        color: #ff4d4f;
        padding: 40px 20px;

        .error-message {
          margin: 0;
          text-align: center;
          max-width: 300px;
        }

        .retry-btn {
          padding: 8px 16px;
          border: 1px solid #ff4d4f;
          border-radius: 4px;
          background: #fff;
          color: #ff4d4f;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: #ff4d4f;
            color: #fff;
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 16px;
        color: #bfbfbf;
        height: 200px;

        p {
          margin: 0;
        }
      }

      .content-wrapper {
        flex: 1;
        overflow: auto;
        padding: 16px;

        .content-text {
          margin: 0;
          padding: 0;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
          line-height: 1.6;
          color: #262626;
          white-space: pre-wrap;
          word-break: break-word;
          background: transparent;
          border: none;
        }
      }
    }

    .preview-footer {
      padding: 8px 16px;
      background: #fafafa;
      border-top: 1px solid #e8e8e8;
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // 暗色主题支持
  @media (prefers-color-scheme: dark) {
    .preview-txt {
      background: #1f1f1f;
      border-color: #434343;
      color: #e8e8e8;

      .preview-header,
      .preview-footer {
        background: #2d2d2d;
        border-color: #434343;
      }

      .file-info .file-name {
        color: #e8e8e8;
      }

      .file-info .file-size {
        background: #434343;
        color: #bfbfbf;
      }

      .action-btn {
        background: #2d2d2d;
        border-color: #434343;
        color: #bfbfbf;

        &:hover:not(:disabled) {
          border-color: #1890ff;
          color: #1890ff;
        }
      }

      .content-text {
        color: #e8e8e8;
      }

      .loading-state {
        color: #8c8c8c;

        .loading-spinner {
          border-color: #434343;
          border-top-color: #1890ff;
        }
      }

      .empty-state {
        color: #595959;
      }
    }
  }
</style>
