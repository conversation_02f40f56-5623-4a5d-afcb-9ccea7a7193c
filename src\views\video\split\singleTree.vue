<template>
  <div style="height: 100%">
    <a-input placeholder="请输入" @change="onChange" allowClear style="padding: 0 10px; width: 100%" />
    <div :class="hasTab ? 'tab-tree-panel-box' : 'tree-panel-tree-box'">
      <div class="loading" v-if="loading">
        <a-spin />
      </div>
      <!-- showIcon -->
      <AntTree
        v-if="treeData.length > 0"
        :tree-data="treeData"
        :default-expanded-keys="expandedKeys"
        :expanded-keys="expandedKeys"
        :auto-expand-parent="autoExpandParent"
        :showIcon="true"
        showLine
        @select="handleNodeClick"
        @expand="onExpand"
        :selectedKeys="selectedKeys"
      >
        <a-icon slot="switcherIcon" type="caret-down" />
        <SvgIcon
          slot="catalogue"
          iconClass="tree-catalogue"
          class="depIcon"
          style="font-size: 18px; margin-bottom: -1px"
        />
        <SvgIcon slot="square" iconClass="视频监控方" class="depIcon" />
        <SvgIcon slot="circle" iconClass="视频监控圆" class="depIcon" />
        <template slot="title" slot-scope="{ title, ext, id, type, allMonitor, onLine }">
          <span v-if="title.indexOf(searchValue) > -1">
            {{ title.substr(0, title.indexOf(searchValue)) }}
            <span style="color: #f50">{{ searchValue }}</span>
            {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
          </span>
          <span v-else :style="ext && ext.onLine == 0 ? { color: '#86909C' } : {}">
            <i
              v-if="type === 'category'"
              :class="[selectList.some(el => el && el.id === id && el.type === type) ? 'online' : 'offline']"
            ></i>
            {{ title }}
            <span v-if="types.includes(type)">
              <span class="online-num">{{ onLine }}</span>
              <span>/</span>
              <span>{{ allMonitor }}</span>
            </span>
          </span>
        </template>
      </AntTree>
    </div>
  </div>
</template>
<script lang="jsx">
  import AntTree from 'ant-design-vue/es/tree'

  // const types = ['project', 'river', 'site', 'other', 'category', 'camera']
  const types = ['project', 'river', 'site', 'other', 'category']

  const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children)
        }
      }
    }
    return parentKey
  }

  function handleTreeData(data) {
    data.forEach(item => {
      item['disabled'] = item.isLeaf
      if (item.children) {
        handleTreeData(item.children)
      }
    })
    return data
  }

  function getIcon(ele) {
    if (ele.type === 'camera') {
      return ele?.cameraType == '1' ? 'square' : 'circle'
    } else {
      return 'catalogue'
    }
  }

  function resetDataSource(data, replaceFields) {
    function dealData(arr) {
      arr.forEach((ele, i) => {
        arr[i] = {
          ...ele,
          key: ele?.[replaceFields.key],
          title: ele?.[replaceFields.title],
          children: ele?.[replaceFields.children],
          slots: { icon: getIcon(ele) },
          scopedSlots: { title: 'title' },
          allMonitor: types.includes(ele.type) ? `${ele.children?.length || 0}` : null,
          onLine: types.includes(ele.type) ? `${ele.children?.filter(el => el.onLine === 1).length || 0}` : null,
        }

        dealData(ele?.[replaceFields.children] || [])
      })
    }
    dealData(data)

    return data
  }

  export default {
    name: 'videoProjectTree',
    components: { AntTree },
    props: {
      treeOptions: {
        type: Object,
        required: true,
      },
      isLeafDisabled: {
        type: Boolean,
      },
      defaultExpandedKeys: {
        type: Array,
        default: () => [],
      },
      hasTab: {
        type: Boolean,
        default: false,
      },
      currentKeys: { default: () => [] },
      selectList: { default: () => [] },
    },

    data() {
      return {
        loading: false,
        treeData: [],

        expandedKeys: this.defaultExpandedKeys,
        key: this.treeOptions.replaceFields.key,
        leafNodes: [],
        searchValue: '',
        autoExpandParent: true,

        dataList: [],
        selectedKeys: this.currentKeys,

        types: types,
      }
    },
    filters: {},
    created() {
      this.getDataSource(undefined, 'created')
    },
    watch: {},
    methods: {
      getExpandedKeys(nodes) {
        if (!nodes || nodes.length === 0) {
          return []
        }

        nodes.forEach(node => {
          this.leafNodes.push(node.key)
          return this.getExpandedKeys(node.children)
        })
      },
      generateList(data) {
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          const key = node.key
          const title = node.title
          this.dataList.push({ key, title })
          if (node.children) {
            this.generateList(node.children)
          }
        }
      },
      // 筛选节点
      onChange(e) {
        const value = e.target.value
        const expandedKeys = this.dataList
          .map(item => {
            if (item.title.indexOf(value) > -1) {
              return getParentKey(item.key, this.treeData)
            }
            return null
          })
          .filter((item, i, self) => item && self.indexOf(item) === i)

        Object.assign(this, {
          expandedKeys,
          searchValue: value,
          autoExpandParent: true,
        })
      },
      // 获取树
      getDataSource(value, type) {
        this.loading = true
        if (this.treeOptions.dataSource?.length > 0 && type != 'search') {
          this.treeData = resetDataSource(this.treeOptions.dataSource, this.treeOptions.replaceFields)
          this.loading = false
          // 设置默认选中第一个节点
          // if (this.selectedKeys.length == 0) {
          //   this.selectedKeys = [this.treeData[0].key]
          // }
          let firstProjectTree = this.getTreeFirstProject(this.treeData)
          this.selectedKeys = [firstProjectTree.key]

          this.generateList(this.treeData)
          this.getExpandedKeys(this.treeData)
          Object.assign(this, {
            expandedKeys: this.leafNodes,
            searchValue: value,
            autoExpandParent: true,
          })
          this.leafNodes = []
          if (type === 'created') {
            // this.$emit('onTreeMounted', this.treeData)
            this.$emit('onTreeMounted', firstProjectTree)
          }
        } else {
          const searchInfo = { keywords: value }
          this.treeOptions
            .getDataApi(searchInfo)
            .then(response => {
              this.loading = false

              if (this.isLeafDisabled) {
                this.treeData = handleTreeData(response?.data || []) //changeIconState(
              }
              this.treeData = resetDataSource(response?.data || [], this.treeOptions.replaceFields)

              // 设置默认选中第一个节点
              // if (this.selectedKeys.length == 0) {
              //   this.selectedKeys = [this.treeData[0].key]
              // }
              let firstProjectTree = this.getTreeFirstProject(this.treeData)
              // this.selectedKeys = [firstProjectTree.key]

              this.generateList(this.treeData)

              this.getExpandedKeys(response.data)
              Object.assign(this, {
                expandedKeys: this.leafNodes,
                searchValue: value,
                autoExpandParent: true,
              })
              this.leafNodes = []
              if (type === 'created') {
                // this.$emit('onTreeMounted', response?.data || [])
                this.$emit('onTreeMounted', firstProjectTree)
              }
            })
            .catch(res => {
              console.log('error', res)
            })
        }
      },
      getTreeFirstProject(treeList) {
        let _this = this
        for (let i = 0; i < treeList.length; i++) {
          let treeItem = treeList[i]
          if (types.includes(treeItem.type)) {
            return treeItem
          } else {
            if (treeItem?.children?.length) {
              let res = _this.getTreeFirstProject(treeItem.children)
              if (res) {
                return res
              }
            }
          }
        }
      },
      //icon
      changeIconState(data) {
        for (let i in data) {
          data[i].slots = { icon: data[i].type }
          if (data[i].children) {
            this.changeIconState(data[i].children)
          }
        }
        return data
      },
      // 节点单击事件,
      handleNodeClick(keys, event) {
        if (!keys?.length) return
        this.selectedKeys = [event.node.eventKey]
        this.$emit('select', event.node)
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');

  ::v-deep .online {
    display: inline-block;
    width: 6px;
    height: 6px;
    background: @primary-color;
    border-radius: 50%;
    vertical-align: middle;
    margin-top: -1px;
  }
  ::v-deep .offline {
    display: inline-block;
    width: 6px;
    height: 6px;
  }
  ::v-deep .online-num {
    color: @primary-color;
  }
  ::v-deep li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
    color: #aaa;
  }
  ::v-deep .ant-tree > li:last-child {
    padding-bottom: 0;
    // margin-bottom: 7px;
  }
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
    // margin-top: 7px;
  }

  // 去掉叶子前的icon
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .anticon-file {
    display: none;
  }
  // 去掉叶子前line
  ::v-deep
    .ant-tree.ant-tree-show-line
    .ant-tree-child-tree
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }
  ::v-deep
    .ant-tree.ant-tree-show-line
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }

  // 展开箭头
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher .ant-tree-switcher-icon {
    color: #666;
    font-size: 14px;
  }

  .tree-panel-tree-box {
    position: relative;
    .loading {
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: center;
      top: 30px;
      z-index: 2;
    }
  }
  ::v-deep .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: transparent;
  }

  ::v-deep .ant-tree li .ant-tree-node-content-wrapper {
    padding-left: 0px;
  }

  ::v-deep .ant-tree li span.ant-tree-iconEle {
    width: 16px;
    height: 16px;
  }

  ::v-deep .ant-tree.ant-tree-show-line li::before {
    left: 7px;
  }

  ::v-deep .ant-tree li ul {
    padding: 0 0 0 15px;
  }
  ::v-deep .svg-icon {
    // width: 14px;
    // height: 14px;
    // vertical-align: middle;
  }
</style>
