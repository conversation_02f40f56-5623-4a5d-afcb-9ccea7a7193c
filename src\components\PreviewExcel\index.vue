<template>
  <div class="excel-preview-container">
    <!-- 加载状态 -->
    <div v-show="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载Excel文件...</p>
    </div>

    <!-- 错误状态 -->
    <div v-show="error && !loading" class="error-container">
      <div class="error-icon">⚠️</div>
      <p>{{ error }}</p>
      <button @click="retry" class="retry-btn">重试</button>
    </div>

    <!-- 预览内容 -->
    <div class="preview-content" :style="{ display: loading || error ? 'none' : 'flex' }">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <span class="file-name">{{ fileName }}</span>
          <span class="file-size" v-if="fileSize">({{ formatFileSize(fileSize) }})</span>
        </div>
      </div>

      <!-- Luckysheet 预览 -->
      <div id="luckysheet-container" class="preview-container"></div>
    </div>
  </div>
</template>

<script>
  import * as XLSX from 'xlsx'

  export default {
    name: 'OfficeExcel',
    props: {
      url: {
        type: String,
        required: true,
      },
      height: {
        type: String,
        default: '100%',
      },
    },
    data() {
      return {
        loading: true,
        error: null,
        fileName: '',
        fileSize: null,
        luckyseetInstance: null,
        resizeTimer: null,
      }
    },
    mounted() {
      this.initPreview()
      // 监听窗口 resize 事件
      window.addEventListener('resize', this.handleWindowResize)
    },
    beforeDestroy() {
      this.cleanup()
      window.removeEventListener('resize', this.handleWindowResize)
    },
    watch: {
      url: {
        handler() {
          this.initPreview()
        },
        immediate: false,
      },
    },
    methods: {
      async initPreview() {
        try {
          this.loading = true
          this.error = null

          // 获取文件信息
          await this.getFileInfo()

          // 初始化 Luckysheet 预览
          await this.initLuckysheetPreview()
        } catch (err) {
          console.error('Excel预览初始化失败:', err)
          this.error = '文件加载失败，请检查文件路径或网络连接'
          this.loading = false
        }
      },

      async getFileInfo() {
        try {
          const response = await fetch(this.url, { method: 'HEAD' })
          if (response.ok) {
            this.fileSize = response.headers.get('content-length')
            const urlParts = this.url.split('/')
            this.fileName = decodeURIComponent(urlParts[urlParts.length - 1])
          }
        } catch (err) {
          console.warn('获取文件信息失败:', err)
          const urlParts = this.url.split('/')
          this.fileName = decodeURIComponent(urlParts[urlParts.length - 1]) || 'Excel文件'
        }
      },

      async initLuckysheetPreview() {
        this.cleanup()
        await this.initLuckysheet()
      },

      async initLuckysheet() {
        try {
          // 动态加载 Luckysheet
          if (!window.luckysheet) {
            await this.loadLuckyseetResources()
          }

          // 获取Excel数据
          const workbookData = await this.fetchExcelData()

          // 初始化 Luckysheet
          this.$nextTick(() => {
            setTimeout(() => {
              const container = document.getElementById('luckysheet-container')

              if (container) {
                container.innerHTML = '' // 清空容器

                try {
                  window.luckysheet.create({
                    container: 'luckysheet-container',
                    data: workbookData,
                    title: this.fileName,
                    lang: 'zh',
                    allowCopy: true,
                    allowEdit: false, // 只读模式
                    showToolbar: false,
                    showSheetTabs: true,
                    showStatisticBar: false,
                    enableAddRow: false,
                    enableAddCol: false,
                    showinfobar: false,
                    showConfigWindowResize: true, // 启用自动 resize
                    hook: {
                      workbookCreateAfter: () => {
                        console.log('✅ Luckysheet workbookCreateAfter 回调触发')
                        // 触发 resize 以确保正确显示
                        this.resizeLuckysheet()
                      },
                    },
                  })

                  // 延迟一点再设置 loading 为 false，确保 Luckysheet 有时间渲染
                  setTimeout(() => {
                    this.loading = false

                    // 等待 DOM 更新后再触发 resize
                    this.$nextTick(() => {
                      setTimeout(() => {
                        this.resizeLuckysheet()
                      }, 200)
                    })
                  }, 800)
                } catch (createError) {
                  console.error('❌ Luckysheet.create 调用失败:', createError)
                  this.error = 'Luckysheet 创建失败: ' + createError.message
                  this.loading = false
                }
              } else {
                console.error('❌ 找不到容器元素 #luckysheet-container')
                this.error = '找不到预览容器'
                this.loading = false
              }
            }, 500)
          })
        } catch (err) {
          console.error('Luckysheet 初始化失败:', err)
          this.error = 'Excel 文件预览失败，请检查文件格式或网络连接'
          this.loading = false
        }
      },

      async loadLuckyseetResources() {
        return new Promise((resolve, reject) => {
          // 检查是否已经加载过 CSS
          if (!document.querySelector('link[href*="luckysheet"]')) {
            // 加载 CSS
            const cssLink = document.createElement('link')
            cssLink.rel = 'stylesheet'
            cssLink.href = 'https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/plugins/css/pluginsCss.css'
            document.head.appendChild(cssLink)

            const cssLink2 = document.createElement('link')
            cssLink2.rel = 'stylesheet'
            cssLink2.href = 'https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/plugins/plugins.css'
            document.head.appendChild(cssLink2)

            const cssLink3 = document.createElement('link')
            cssLink3.rel = 'stylesheet'
            cssLink3.href = 'https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/css/luckysheet.css'
            document.head.appendChild(cssLink3)
          }

          // 检查是否已经加载过 JS
          if (document.querySelector('script[src*="luckysheet"]')) {
            resolve()
            return
          }

          // 加载 JS
          const script = document.createElement('script')
          script.src = 'https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/luckysheet.umd.js'
          script.onload = resolve
          script.onerror = reject
          document.head.appendChild(script)
        })
      },

      async fetchExcelWorkbook() {
        const response = await fetch(this.url, {
          mode: 'cors',
          cache: 'no-cache',
        })
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const arrayBuffer = await response.arrayBuffer()
        const workbook = XLSX.read(arrayBuffer, { type: 'array' })
        return workbook
      },

      async fetchExcelData() {
        const workbook = await this.fetchExcelWorkbook()
        const sheets = []

        workbook.SheetNames.forEach((sheetName, index) => {
          const worksheet = workbook.Sheets[sheetName]
          const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1')

          const data = []
          for (let row = range.s.r; row <= range.e.r; row++) {
            const rowData = []
            for (let col = range.s.c; col <= range.e.c; col++) {
              const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
              const cell = worksheet[cellAddress]
              rowData.push({
                v: cell ? cell.v : '',
                ct: { fa: 'General', t: 'n' },
              })
            }
            data.push(rowData)
          }

          sheets.push({
            name: sheetName,
            data: data,
            index: index,
            status: 1,
          })
        })

        return sheets
      },

      formatFileSize(bytes) {
        if (!bytes) return ''
        const sizes = ['B', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(1024))
        return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i]
      },

      retry() {
        this.initPreview()
      },

      resizeLuckysheet() {
        try {
          if (window.luckysheet && window.luckysheet.resize) {
            window.luckysheet.resize()
          } else {
            // 如果没有 resize 方法，尝试触发窗口 resize 事件
            window.dispatchEvent(new Event('resize'))
          }
        } catch (err) {
          console.warn('Luckysheet resize 失败:', err)
        }
      },

      handleWindowResize() {
        // 防抖处理，避免频繁调用
        clearTimeout(this.resizeTimer)
        this.resizeTimer = setTimeout(() => {
          this.resizeLuckysheet()
        }, 100)
      },

      cleanup() {
        if (this.luckyseetInstance) {
          try {
            window.luckysheet.destroy()
          } catch (err) {
            console.warn('Luckysheet 清理失败:', err)
          }
        }
      },
    },
  }
</script>

<style scoped>
  .excel-preview-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f5f5f5;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #f56c6c;
  }

  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .retry-btn {
    margin-top: 16px;
    padding: 8px 16px;
    background: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .retry-btn:hover {
    background: #66b1ff;
  }

  .preview-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .toolbar {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .file-name {
    font-weight: 500;
    color: #303133;
  }

  .file-size {
    color: #909399;
    font-size: 12px;
  }

  .preview-container {
    flex: 1;
    background: white;
    overflow: hidden;
  }

  #luckysheet-container {
    width: 100%;
    height: 100%;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .toolbar {
      justify-content: center;
    }
  }
</style>
