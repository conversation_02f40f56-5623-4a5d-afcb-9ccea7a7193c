// 卫星图表示加载的地图类型，img用于拼接地图url字符串 

export const TianDiTu_Items = [
    {
        label: '地形图',
        bg: require('@/assets/images/map-terrain.png'),
        urlTag: 'ter',
      },
      {
        label: '卫星图',
        bg: require('@/assets/images/map-satellite.png'),
        urlTag: 'img',
      },
      {
        label: '矢量图',
        bg: require('@/assets/images/map-street.png'),
        urlTag: 'vec',
      },
    ];

const urlDict = {}
function generateUrl(tag) {
    return [
        `${process.env.VUE_APP_TIANDI_BASE}/${tag}_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=${tag}&tilematrix={z}&tilerow={y}&tilecol={x}`,
        `${process.env.VUE_APP_TIANDI_BASE}/c${tag[0]}a_w/wmts?tk=${process.env.VUE_APP_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=c${tag[0]}a&tilematrix={z}&tilerow={y}&tilecol={x}`
    ]
}

TianDiTu_Items.forEach(item => {
    let {label,urlTag} = item
    let [tagUrl1, tagUrl2] = generateUrl(urlTag)
    urlDict[label] = [
        {
            id: 'mapbox-wmts-base-layer',
            type: 'raster',
            source: {
                type: 'raster',
                tiles: [tagUrl1],
                tileSize: 256,
            },
        },
        {
            id: 'mapbox-wmts-base-layer1',
            type: 'raster',
            source: {
                type: 'raster',
                tiles: [tagUrl2],
                tileSize: 256,
            },
        }
    ]
})

function getTianDiTuMapByConfig(tag) {
    if (!urlDict[tag]) throw new Error('没有找到对应的地图类型')
    return urlDict[tag]
}


export default getTianDiTuMapByConfig;