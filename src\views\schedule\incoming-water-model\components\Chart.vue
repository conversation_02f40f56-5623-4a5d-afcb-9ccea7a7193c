<template>
  <BarAndLineMix
    :dataSource="chartData"
    :markLineXAxis="showForecastBaseline && dataSource?.reals?.length ? dataSource.reals.length - 1 : null"
    :markPointsData="markPointsData"
  />
</template>

<script>
  import BarAndLineMix from './BarAndLineMix.vue'
  import { maxBy } from 'lodash'

  export default {
    name: 'Chart',
    props: {
      dataSource: {
        type: Object,
        required: true
      },
      showForecastBaseline: {
        type: Boolean,
        default: true
      },
      // 场景类型：'forecast' - 来水预报，'inversion' - 来水反演
      scenario: {
        type: String,
        default: 'forecast',
        validator: value => ['forecast', 'inversion'].includes(value)
      }
    },
    components: {
      BarAndLineMix,
    },
    data() {
      return {
        chartData: [],
      }
    },
    computed: {
      markPointsData() {
        // 添加安全检查，确保数据存在
        if (!this.dataSource || !this.dataSource.fcsts || !Array.isArray(this.dataSource.fcsts)) {
          return {
            water: [],
            flow: [],
          }
        }

        const predFlow = maxBy(this.dataSource.fcsts, 'wlv')
        const realsLength = this.dataSource.reals ? this.dataSource.reals.length : 0

        const predFlowMax = {
          x:
            this.dataSource.fcsts.findIndex(el => (predFlow ? predFlow.tm === el.tm : false)) +
            realsLength,
          y: predFlow?.inflow,
        }

        const predWater = maxBy(this.dataSource.fcsts, 'wlv')

        const predWaterMax = {
          x:
            this.dataSource.fcsts.findIndex(el => (predWater ? predWater.tm === el.tm : false)) +
            realsLength,
          y: predWater?.wlv,
        }

        return {
          water: [{ name: '预测', value: predWaterMax.y, xAxis: predWaterMax.x, yAxis: predWaterMax.y }],
          flow: [{ value: predFlowMax.y, xAxis: predFlowMax.x, yAxis: predFlowMax.y }],
        }
      },
    },
    watch: {
      dataSource: {
        handler(newVal) {
          // 添加安全检查，确保数据存在
          if (!newVal) {
            this.chartData = []
            return
          }

          const data = newVal
          console.log('data', data)

          // 确保 reals 和 fcsts 数组存在
          const reals = data.reals || []
          const fcsts = data.fcsts || []

          // 根据场景类型构建不同的图表数据
          this.buildChartDataByScenario(reals, fcsts)
        },
        deep: true,
        immediate: true,
      },
    },
    methods: {
      buildChartDataByScenario(reals, fcsts) {
        if (this.scenario === 'forecast') {
          // 来水预报场景：时段雨量、累计雨量、来水流量
          this.buildForecastChartData(reals, fcsts)
        } else if (this.scenario === 'inversion') {
          // 来水反演场景：降雨量、累计降雨量、来水流量、出库流量、水位
          this.buildInversionChartData(reals, fcsts)
        }
      },

      buildForecastChartData(reals, fcsts) {
        const rainData = {
          name: '时段雨量',
          data: reals.map(el => [el.tm, el.rain]).concat(fcsts.map(el => [el.tm, el.rain])),
        }
        const sumRainData = {
          name: '累计降雨量',
          data: rainData.data.map((el, idx) => {
            const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
            return [el[0], +sum.toFixed(1)]
          }),
        }

        this.chartData = [
          rainData,
          sumRainData,
          {
            name: '来水流量',
            data: reals.map(el => [el.tm, el.inflow]).concat(fcsts.map(el => [el.tm, el.inflow])),
          },
        ]
      },

      buildInversionChartData(reals, fcsts) {
        const rainData = {
          name: '时段雨量',
          data: reals.map(el => [el.tm, el.rain]).concat(fcsts.map(el => [el.tm, el.rain])),
        }
        const sumRainData = {
          name: '累计降雨量',
          data: rainData.data.map((el, idx) => {
            const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
            return [el[0], +sum.toFixed(1)]
          }),
        }

        this.chartData = [
          rainData,
          sumRainData,
          {
            name: '来水流量',
            data: reals.map(el => [el.tm, el.inflow]).concat(fcsts.map(el => [el.tm, el.inflow])),
          },
          {
            name: '出库流量',
            data: reals.map(el => [el.tm, el.outflow]).concat(fcsts.map(el => [el.tm, el.outflow])),
          },
          {
            name: '水位',
            data: reals.map(el => [el.tm, el.wlv]).concat(fcsts.map(el => [el.tm, el.wlv])),
          },
        ]
      }
    },
    
    created() {},
  }
</script>

<style lang="less" scoped></style>
